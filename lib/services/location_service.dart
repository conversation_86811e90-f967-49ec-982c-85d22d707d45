import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:geolocator/geolocator.dart';

/// 位置服务类，用于获取和管理用户位置
class LocationService {
  static const String _lastLocationKey = 'last_location';

  // 默认位置（北京天安门）
  static const LatLng defaultLocation = LatLng(39.9087, 116.3976);

  // 用户当前位置
  LatLng _currentLocation = defaultLocation;

  // 单例模式
  static final LocationService _instance = LocationService._internal();

  factory LocationService() {
    return _instance;
  }

  LocationService._internal();

  /// 初始化位置服务
  Future<void> initialize() async {
    // 首先加载上次保存的位置
    await _loadLastLocation();

    // 异步获取当前位置，不阻塞应用启动
    _updateLocationAsync();
  }

  // 缓存上次检查权限的时间，避免频繁检查
  DateTime? _lastPermissionCheck;
  LocationPermission? _cachedPermission;

  /// 异步更新位置信息
  Future<void> _updateLocationAsync() async {
    try {
      // 检查位置服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('位置服务未启用');
        return;
      }

      // 检查权限（使用缓存减少频繁检查）
      final now = DateTime.now();
      if (_lastPermissionCheck == null ||
          now.difference(_lastPermissionCheck!).inMinutes > 10 ||
          _cachedPermission == null) {
        // 超过10分钟或首次检查，重新获取权限状态
        _cachedPermission = await Geolocator.checkPermission();
        _lastPermissionCheck = now;
      }

      LocationPermission permission = _cachedPermission!;

      // 处理权限问题
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        _cachedPermission = permission; // 更新缓存
        _lastPermissionCheck = now;

        if (permission == LocationPermission.denied) {
          debugPrint('用户拒绝了位置权限');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('用户永久拒绝了位置权限，请到应用设置中开启');
        return;
      }

      // 权限已授予，获取当前位置
      // 使用较低精度和超时设置以提高性能
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium, // 使用中等精度，提高速度
          timeLimit: Duration(seconds: 5), // 设置超时时间
        ),
      );

      final newLocation = LatLng(position.latitude, position.longitude);

      // 只有当位置变化超过10米时才更新
      if (_currentLocation == defaultLocation ||
          calculateDistance(_currentLocation, newLocation) > 0.01) {
        _currentLocation = newLocation;
        await _saveLastLocation();
        debugPrint('位置已更新: $_currentLocation');
      }
    } catch (e) {
      debugPrint('获取位置失败: $e');
      // 获取位置失败，继续使用上次加载的位置或默认位置
    }
  }

  /// 获取当前位置
  LatLng getCurrentLocation() {
    return _currentLocation;
  }

  /// 设置当前位置
  Future<void> setCurrentLocation(LatLng location) async {
    _currentLocation = location;
    await _saveLastLocation();
  }

  /// 计算两点之间的距离（公里）
  double calculateDistance(LatLng point1, LatLng point2) {
    const Distance distance = Distance();
    final double meters = distance(point1, point2);
    return meters / 1000; // 转换为公里
  }

  /// 计算点与当前位置的距离（公里）
  double calculateDistanceFromCurrent(LatLng point) {
    return calculateDistance(_currentLocation, point);
  }

  /// 从本地存储加载上次位置
  Future<void> _loadLastLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationString = prefs.getString(_lastLocationKey);

      if (locationString != null) {
        final parts = locationString.split(',');
        if (parts.length == 2) {
          final lat = double.tryParse(parts[0]);
          final lng = double.tryParse(parts[1]);

          if (lat != null && lng != null) {
            _currentLocation = LatLng(lat, lng);
          }
        }
      }
    } catch (e) {
      // 处理错误，使用默认位置
      _currentLocation = defaultLocation;
    }
  }

  /// 保存当前位置到本地存储
  Future<void> _saveLastLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationString =
          '${_currentLocation.latitude},${_currentLocation.longitude}';
      await prefs.setString(_lastLocationKey, locationString);
    } catch (e) {
      // 处理保存错误
      debugPrint('保存位置失败: $e');
    }
  }

  // 缓存上次请求位置的时间和结果
  DateTime _lastLocationRequest = DateTime.now().subtract(
    const Duration(minutes: 5),
  );
  LatLng? _lastRequestResult;

  /// 请求更新当前位置
  ///
  /// 为了提高性能，如果短时间内（10秒内）多次请求位置，会返回缓存的结果
  Future<LatLng> requestLocationUpdate() async {
    final now = DateTime.now();

    // 如果距离上次请求不到10秒，且有缓存结果，直接返回缓存
    if (now.difference(_lastLocationRequest).inSeconds < 10 &&
        _lastRequestResult != null) {
      debugPrint('使用缓存的位置结果');
      return _lastRequestResult!;
    }

    try {
      // 使用高精度获取位置
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      final newLocation = LatLng(position.latitude, position.longitude);

      // 更新当前位置和缓存
      _currentLocation = newLocation;
      _lastRequestResult = newLocation;
      _lastLocationRequest = now;

      // 保存到本地存储
      await _saveLastLocation();

      return newLocation;
    } catch (e) {
      debugPrint('请求位置更新失败: $e');

      // 更新缓存时间，但保留旧结果
      _lastLocationRequest = now;
      _lastRequestResult = _currentLocation;

      return _currentLocation; // 返回当前位置（可能是上次保存的位置或默认位置）
    }
  }
}
